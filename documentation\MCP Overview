Model Context Protocol (MCP)

Copy Page
You can build and deploy Model Context Protocol (MCP) ↗ servers on Cloudflare.

What is the Model Context Protocol (MCP)?
Model Context Protocol (MCP) ↗ is an open standard that connects AI systems with external applications. Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various accessories, MCP provides a standardized way to connect AI agents to different services.

MCP Terminology
MCP Hosts: AI assistants (like <PERSON> or <PERSON><PERSON><PERSON> ↗), AI agents, or applications that need to access external capabilities.
MCP Clients: Clients embedded within the MCP hosts that connect to MCP servers and invoke tools. Each MCP client instance has a single connection to an MCP server.
MCP Servers: Applications that expose tools, prompts ↗, and resources ↗ that MCP clients can use.
Remote vs. local MCP connections
The MCP standard supports two modes of operation:

Remote MCP connections: MCP clients connect to MCP servers over the Internet, establishing a long-lived connection using HTTP and Server-Sent Events (SSE), and authorizing the MCP client access to resources on the user's account using OAuth.
Local MCP connections: MCP clients connect to MCP servers on the same machine, using stdio ↗ as a local transport method.
Best Practices
Tool design: Do not treat your MCP server as a wrapper around your full API schema. Instead, build tools that are optimized for specific user goals and reliable outcomes. Fewer, well-designed tools often outperform many granular ones, especially for agents with small context windows or tight latency budgets.
Scoped permissions: Deploying several focused MCP servers, each with narrowly scoped permissions, reduces the risk of over-privileged access and makes it easier to manage and audit what each server is allowed to do.
Tool descriptions: Detailed parameter descriptions help agents understand how to use your tools correctly — including what values are expected, how they affect behavior, and any important constraints. This reduces errors and improves reliability.
Evaluation tests: Use evaluation tests ('evals') to measure the agent’s ability to use your tools correctly. Run these after any updates to your server or tool descriptions to catch regressions early and track improvements over time.
Get Started