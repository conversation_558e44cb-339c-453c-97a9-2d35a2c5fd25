import { McpA<PERSON> } from "agents/mcp";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";

const API_ENDPOINT = 'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/v1/mcp';
const API_KEY = 'Bearer 9ea0e4f8868020aacf2479d1b4514bee';

// Define our MCP agent with Guru AI tools
export class MyMCP extends McpAgent {
    server = new McpServer({
        name: "The Guru AI",
        version: "1.0.0",
    });

    private availableModels: string[] = [];

    async init() {
        // Fetch available models first
        await this.fetchAvailableModels();

        // Guru AI request tool
        this.server.tool(
            "guru_ai_request",
            {
                model: z.string().describe("The AI model to use"),
                prompt: z.string().describe("The prompt to send to the AI model"),
            },
            async ({ model, prompt }) => {
                try {
                    // Validate model
                    if (!this.isValidModel(model)) {
                        return {
                            content: [
                                {
                                    type: "text",
                                    text: `Invalid model: ${model}. Available models: ${this.availableModels.slice(0, 5).join(', ')}${this.availableModels.length > 5 ? ', and more...' : ''}`,
                                },
                            ],
                        };
                    }

                    const formattedPrompt = `Guru, ${model}: ${prompt}`;

                    const response = await fetch(API_ENDPOINT, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': API_KEY,
                        },
                        body: JSON.stringify({ prompt: formattedPrompt }),
                    });

                    if (!response.ok) {
                        throw new Error(`API Error: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();

                    if (data && data.response) {
                        return { content: [{ type: "text", text: data.response }] };
                    } else {
                        throw new Error('Invalid response format from The Guru AI service');
                    }
                } catch (error) {
                    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
                    return {
                        content: [{ type: "text", text: `Error: ${errorMessage}` }],
                    };
                }
            }
        );
    }

    // Fetch available models from The Guru AI service
    private async fetchAvailableModels() {
        try {
            const response = await fetch(
                'https://63660191-5465-48d1-a384-83fe8ffc5b94-00-k5l1b9vlgd3n.janeway.replit.dev/api/models',
                {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!response.ok) {
                throw new Error(`Failed to fetch models: ${response.status}`);
            }

            const data = await response.json();

            if (data && Array.isArray(data)) {
                // Filter only available models
                this.availableModels = data
                    .filter(model => model.available)
                    .map(model => model.name);

                console.log(`Loaded ${this.availableModels.length} available models`);
            } else {
                console.error('Failed to parse available models, using default models');
                // Fallback to default models if we can't fetch them
                this.availableModels = ['gpt-4o', 'meta-llama/llama-3-70b-instruct'];
            }
        } catch (error) {
            console.error('Error fetching available models:', error);
            // Fallback to default models if we can't fetch them
            this.availableModels = ['gpt-4o', 'meta-llama/llama-3-70b-instruct'];
        }
    }

    // Check if the model is valid
    private isValidModel(model: string): boolean {
        return this.availableModels.includes(model);
    }
}

export default {
    fetch(request: Request, env: Env, ctx: ExecutionContext) {
        const url = new URL(request.url);

        if (url.pathname === "/sse" || url.pathname === "/sse/message") {
            return MyMCP.serveSSE("/sse").fetch(request, env, ctx);
        }

        if (url.pathname === "/mcp") {
            return MyMCP.serve("/mcp").fetch(request, env, ctx);
        }

        return new Response("Not found", { status: 404 });
    },
};
