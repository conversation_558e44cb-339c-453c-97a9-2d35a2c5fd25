Cloudflare Workers

Copy Page
A serverless platform for building, deploying, and scaling apps across Cloudflare's global network ↗ with a single command — no infrastructure to manage, no complex configuration

With Cloudflare Workers, you can expect to:

Deliver fast performance with high reliability anywhere in the world
Build full-stack apps with your framework of choice, including React, Vue, Svelte, Next, Astro, React Router, and more
Use your preferred language, including JavaScript, TypeScript, Python, Rust, and more
Gain deep visibility and insight with built-in observability
Get started for free and grow with flexible pricing, affordable at any scale
Get started with your first project:

Deploy a template 
Deploy with Wrangler CLI

Build with Workers
Front-end applications
Deploy static assets to Cloudflare's CDN & cache for fast rendering
Back-end applications
Build APIs and connect to data stores with Smart Placement to optimize latency
Serverless AI inference
Run LLMs, generate images, and more with Workers AI
Background jobs
Schedule cron jobs, run durable Workflows, and integrate with Queues
Integrate with Workers
Connect to external services like databases, APIs, and storage via Bindings, enabling functionality with just a few lines of code:

Storage

Durable Objects
Scalable stateful storage for real-time coordination.

D1
Serverless SQL database built for fast, global queries.

KV
Low-latency key-value storage for fast, edge-cached reads.

Queues
Guaranteed delivery with no charges for egress bandwidth.

Hyperdrive
Connect to your external database with accelerated queries, cached at the edge.

Compute

Workers AI
Machine learning models powered by serverless GPUs.

Workflows
Durable, long-running operations with automatic retries.

Vectorize
Vector database for AI-powered semantic search.

R2
Zero-egress object storage for cost-efficient data access.

Browser Rendering
Programmatic serverless browser instances.

Media

Cache / CDN
Global caching for high-performance, low-latency delivery.

Images
Streamlined image infrastructure from a single API.

Want to connect with the Workers community? Join our Discord ↗