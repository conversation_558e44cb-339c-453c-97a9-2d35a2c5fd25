Tools

Copy Page
Model Context Protocol (MCP) tools are functions that a MCP Server provides and MCP clients can call.

When you build MCP Servers with the @cloudflare/model-context-protocol package, you can define tools the same way as shown in the @modelcontextprotocol/typescript-sdk package's examples ↗.

For example, the following code from this example MCP server ↗ defines a simple MCP server that adds two numbers together:

JavaScript
TypeScript
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp";
import { McpAgent } from "agents/mcp";

export class MyMCP extends McpAgent {
  server = new McpServer({ name: "Demo", version: "1.0.0" });
  async init() {
    this.server.tool(
      "add",
      { a: z.number(), b: z.number() },
      async ({ a, b }) => ({
        content: [{ type: "text", text: String(a + b) }],
      }),
    );
  }
}